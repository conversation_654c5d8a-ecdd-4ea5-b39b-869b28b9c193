import { useState } from 'react';
import { Plus, Users, Hash } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { useCreateChannel } from '@/hooks/chat';
import { useChannelMembers } from '@/hooks/chat/use-channel-members';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { HubParticipantResponse } from '@/lib/types/api';

interface CreateChannelModalProps {
  hubId: number;
  isOpen: boolean;
  onClose: () => void;
  onChannelCreated?: (channelId: number) => void;
}

interface CreateChannelForm {
  name: string;
  description: string;
  participantIds: number[];
}

export function CreateChannelModal({ 
  hubId, 
  isOpen, 
  onClose, 
  onChannelCreated 
}: CreateChannelModalProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const [selectedParticipants, setSelectedParticipants] = useState<number[]>([]);

  const { data: membersResponse, isLoading: loadingMembers } = useChannelMembers(
    hubId, 
    0, // Dummy channel ID since we're fetching hub participants
    { enabled: isOpen }
  );

  const members = membersResponse?.content || [];
  const createChannelMutation = useCreateChannel(hubId);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid },
    watch
  } = useForm<CreateChannelForm>({
    defaultValues: {
      name: '',
      description: '',
      participantIds: []
    }
  });

  const channelName = watch('name');

  const handleClose = () => {
    reset();
    setSelectedParticipants([]);
    onClose();
  };

  const handleParticipantToggle = (participantId: number) => {
    setSelectedParticipants(prev => 
      prev.includes(participantId)
        ? prev.filter(id => id !== participantId)
        : [...prev, participantId]
    );
  };

  const onSubmit = async (data: CreateChannelForm) => {
    if (selectedParticipants.length === 0) {
      toast.error(t(keys.collaborationHubs.chat.noParticipantsSelected));
      return;
    }

    try {
      const response = await createChannelMutation.mutateAsync({
        params: { path: { hubId } },
        body: {
          name: data.name,
          description: data.description || undefined,
          participant_ids: selectedParticipants
        }
      });

      toast.success(t(keys.collaborationHubs.chat.channelCreated));
      
      if (onChannelCreated && response.id) {
        onChannelCreated(response.id);
      }
      
      handleClose();
    } catch (error) {
      console.error('Failed to create channel:', error);
      toast.error(t(keys.collaborationHubs.chat.failedToCreateChannel));
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleBadgeColor = (role?: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'reviewer': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'content_creator': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={cn(
        "max-w-2xl max-h-[90vh]",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
      )}>
        <DialogHeader className={cn(
          "pb-4",
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            "flex items-center gap-2",
            isMobile && "text-base"
          )}>
            <Plus className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
            {t(keys.collaborationHubs.chat.createCustomChannel)}
          </DialogTitle>
          <DialogDescription>
            Create a custom channel with selected participants for focused discussions.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <ScrollArea className={cn(
            "max-h-[60vh]",
            isMobile && "max-h-[calc(100vh-12rem)] px-4"
          )}>
            <div className="space-y-6 pr-4">
              {/* Channel Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  {t(keys.collaborationHubs.chat.channelName)}
                </Label>
                <Input
                  id="name"
                  {...register('name', { 
                    required: 'Channel name is required',
                    maxLength: { value: 255, message: 'Channel name must not exceed 255 characters' }
                  })}
                  placeholder={t(keys.collaborationHubs.chat.channelNamePlaceholder)}
                  className={errors.name ? 'border-destructive' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-destructive">{errors.name.message}</p>
                )}
              </div>

              {/* Channel Description */}
              <div className="space-y-2">
                <Label htmlFor="description">
                  {t(keys.collaborationHubs.chat.channelDescription)}
                </Label>
                <Textarea
                  id="description"
                  {...register('description', {
                    maxLength: { value: 500, message: 'Description must not exceed 500 characters' }
                  })}
                  placeholder={t(keys.collaborationHubs.chat.channelDescriptionPlaceholder)}
                  rows={3}
                  className={errors.description ? 'border-destructive' : ''}
                />
                {errors.description && (
                  <p className="text-sm text-destructive">{errors.description.message}</p>
                )}
              </div>

              {/* Participant Selection */}
              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {t(keys.collaborationHubs.chat.selectParticipants)}
                </Label>
                
                {selectedParticipants.length > 0 && (
                  <p className="text-sm text-muted-foreground">
                    {t(keys.collaborationHubs.chat.participantsSelected, { count: selectedParticipants.length })}
                  </p>
                )}

                {loadingMembers ? (
                  <div className="space-y-2">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-center gap-3 p-2">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div className="flex-1 space-y-1">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-48" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {members.map((member: HubParticipantResponse) => (
                      <div 
                        key={member.id}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <Checkbox
                          id={`participant-${member.id}`}
                          checked={selectedParticipants.includes(member.id!)}
                          onCheckedChange={() => handleParticipantToggle(member.id!)}
                        />
                        
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="text-sm font-medium">
                            {getInitials(member.name || '')}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">
                            {member.name}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {member.email}
                          </p>
                        </div>
                        
                        <Badge className={getRoleBadgeColor(member.role)}>
                          {member.role?.replace('_', ' ')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>

          {/* Actions */}
          <div className={cn(
            "flex justify-end gap-3 pt-4 border-t",
            isMobile && "px-4 pb-4"
          )}>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={createChannelMutation.isPending}
            >
              {t(keys.common.cancel)}
            </Button>
            <Button
              type="submit"
              disabled={!isValid || selectedParticipants.length === 0 || createChannelMutation.isPending}
            >
              {createChannelMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                  {t(keys.collaborationHubs.chat.creating)}
                </>
              ) : (
                t(keys.collaborationHubs.chat.create)
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
