/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/api/posts/{postId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get post details
         * @description Gets detailed information about a specific post
         */
        get: operations["getPost"];
        /**
         * Update a post
         * @description Updates an existing post
         */
        put: operations["updatePost"];
        post?: never;
        /**
         * Delete a post
         * @description Soft deletes a post and removes associated media files from S3. Only post creators and hub admins can delete posts.
         */
        delete: operations["deletePost"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/invoices/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get invoice by ID
         * @description Retrieves a specific invoice by its ID
         */
        get: operations["getInvoiceById"];
        /**
         * Update invoice
         * @description Updates an existing invoice
         */
        put: operations["updateInvoice"];
        post?: never;
        /**
         * Delete invoice
         * @description Soft deletes an invoice
         */
        delete: operations["deleteInvoice"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get collaboration hub details
         * @description Retrieves detailed information about a specific collaboration hub
         */
        get: operations["getHubDetails"];
        /**
         * Update collaboration hub
         * @description Updates an existing collaboration hub (admin only)
         */
        put: operations["updateHub"];
        post?: never;
        /**
         * Delete collaboration hub
         * @description Deletes a collaboration hub and all associated data (admin only)
         */
        delete: operations["deleteHub"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/participants/{participantId}/role": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update participant role
         * @description Updates the role of a hub participant (admin permission required)
         */
        put: operations["updateParticipantRole"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/chats/{channelId}/participants": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Add participants to channel
         * @description Adds participants to a custom chat channel
         */
        put: operations["addParticipantsToChannel"];
        post?: never;
        /**
         * Remove participants from channel
         * @description Removes participants from a custom chat channel
         */
        delete: operations["removeParticipantsFromChannel"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/briefs/{briefId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get collaboration brief by ID
         * @description Retrieves a specific brief within a collaboration hub
         */
        get: operations["getBriefById"];
        /**
         * Update collaboration brief
         * @description Updates an existing brief within a collaboration hub
         */
        put: operations["updateBrief"];
        post?: never;
        /**
         * Delete collaboration brief
         * @description Deletes a brief within a collaboration hub
         */
        delete: operations["deleteBrief"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/comments/{commentId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get a comment
         * @description Retrieves a specific comment by ID
         */
        get: operations["getComment"];
        /**
         * Update a comment
         * @description Updates the content of an existing comment
         */
        put: operations["updateComment"];
        post?: never;
        /**
         * Delete a comment
         * @description Deletes an existing comment
         */
        delete: operations["deleteComment"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/chats/{channelId}/messages/{messageId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * Update chat message
         * @description Updates an existing chat message (only by the message author)
         */
        put: operations["updateMessage"];
        post?: never;
        /**
         * Delete chat message
         * @description Deletes a chat message (only by the message author)
         */
        delete: operations["deleteMessage"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/brands/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get brand by ID
         * @description Retrieves a specific brand with its contacts
         */
        get: operations["getBrandById"];
        /**
         * Update brand
         * @description Updates an existing brand and its contacts
         */
        put: operations["updateBrand"];
        post?: never;
        /**
         * Delete brand
         * @description Soft deletes a brand and all its contacts
         */
        delete: operations["deleteBrand"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/bank-details/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get bank detail by ID
         * @description Retrieves a specific bank detail by ID
         */
        get: operations["getBankDetailsById"];
        /**
         * Update bank detail
         * @description Updates an existing bank detail
         */
        put: operations["updateBankDetails"];
        post?: never;
        /**
         * Delete bank detail
         * @description Soft deletes a bank detail
         */
        delete: operations["deleteBankDetails"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/account-companies/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get account company by ID
         * @description Retrieves a specific account company by ID
         */
        get: operations["getAccountCompanyById"];
        /**
         * Update account company
         * @description Updates an existing account company
         */
        put: operations["updateAccountCompany"];
        post?: never;
        /**
         * Delete account company
         * @description Soft deletes an account company
         */
        delete: operations["deleteAccountCompany"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/posts/{postId}/reviews": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Submit or update a post review
         * @description Submits or updates a review for a post. Only assigned reviewers and hub admins can review posts. Uses upsert behavior - one review per reviewer per post.
         */
        post: operations["submitReview"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/posts/{postId}/comments": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get comments for a post
         * @description Retrieves paginated comments for the specified post
         */
        get: operations["getCommentsForPost"];
        put?: never;
        /**
         * Create a comment on a post
         * @description Creates a new comment on the specified post
         */
        post: operations["createComment"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/posts/media/upload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Upload media file
         * @description Uploads a media file (image or video) for posts
         */
        post: operations["uploadMedia"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/invoices": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get all invoices
         * @description Retrieves all invoices for the account with optional filtering
         */
        get: operations["getAllInvoices"];
        put?: never;
        /**
         * Create invoice
         * @description Creates a new invoice
         */
        post: operations["createInvoice"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/invoices/{id}/send": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Send invoice email
         * @description Sends invoice email to all recipients with PDF attachment
         */
        post: operations["sendInvoiceEmail"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get collaboration hubs
         * @description Retrieves a paginated list of collaboration hubs with optional filtering
         */
        get: operations["getHubs"];
        put?: never;
        /**
         * Create a new collaboration hub
         * @description Creates a new collaboration hub and automatically adds the creator as an admin participant
         */
        post: operations["createHub"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/posts": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List posts
         * @description Gets a paginated list of posts with filtering
         */
        get: operations["getPosts"];
        put?: never;
        /**
         * Create a new post
         * @description Creates a new post in a collaboration hub. Post must have either caption or media content (or both). Only content creators, admins, and reviewer-creators can create posts.
         */
        post: operations["createPost"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/participants": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List hub participants
         * @description Gets a paginated list of hub participants with optional filtering
         */
        get: operations["getHubParticipants"];
        put?: never;
        /**
         * Invite participants to hub
         * @description Invites internal users, external users, or brand contacts to a collaboration hub
         */
        post: operations["inviteParticipants"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/participants/{participantId}/resend-invite": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Resend invitation
         * @description Resends invitation email to an external participant (admin permission required)
         */
        post: operations["resendInvitation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/media/validate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Validate uploaded file
         * @description Validates that a file was uploaded successfully via presigned URL. Works for both internal users and external participants with hub access.
         */
        post: operations["validateUploadedFile"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/media/presigned-upload-url": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate presigned upload URL
         * @description Generates a presigned URL for direct S3 upload with enforced constraints. Works for both internal users and external participants with hub access.
         */
        post: operations["generatePresignedUploadUrl"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/media/download-url": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate presigned download URL
         * @description Generates a presigned URL for secure file download. Works for both internal users and external participants with hub access.
         */
        post: operations["generatePresignedDownloadUrl"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/media/create-record": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create media record after presigned upload
         * @description Creates a media record after successful presigned URL upload. Works for both internal users and external participants with hub access.
         */
        post: operations["createMediaRecord"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/media/batch-presigned-upload-urls": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Generate batch presigned upload URLs
         * @description Generates multiple presigned URLs for concurrent S3 uploads with enforced constraints. Works for both internal users and external participants with hub access.
         */
        post: operations["generateBatchPresignedUploadUrls"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/chats": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List chat channels
         * @description Gets all chat channels accessible to the current user in the specified hub with pagination
         */
        get: operations["getChannels"];
        put?: never;
        /**
         * Create custom chat channel
         * @description Creates a new custom chat channel with selected participants
         */
        post: operations["createCustomChannel"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/briefs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get collaboration briefs
         * @description Retrieves a paginated list of briefs within a collaboration hub with optional filtering
         */
        get: operations["getBriefs"];
        put?: never;
        /**
         * Create a new collaboration brief
         * @description Creates a new brief within a collaboration hub
         */
        post: operations["createBrief"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/chats/{channelId}/messages": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get chat messages
         * @description Gets messages from a chat channel with pagination support for infinite scroll
         */
        get: operations["getMessages"];
        put?: never;
        /**
         * Send chat message
         * @description Sends a new message to the specified chat channel
         */
        post: operations["sendMessage"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/brands": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get brands with pagination
         * @description Retrieves active brands for the current account with filtering and pagination
         */
        get: operations["getBrands"];
        put?: never;
        /**
         * Create a new brand
         * @description Creates a new brand with associated contacts
         */
        post: operations["createBrand"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/bank-details": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get all bank details
         * @description Retrieves all active bank details for the current account
         */
        get: operations["getAllBankDetails"];
        put?: never;
        /**
         * Create bank detail
         * @description Creates a new bank detail
         */
        post: operations["createBankDetails"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/auth/verify-email": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["verifyEmail"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/auth/register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["register"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/auth/refresh": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["refresh"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/auth/magic-link/authenticate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Authenticate with magic link
         * @description Authenticates external user via magic link token and returns access token
         */
        post: operations["authenticateWithMagicLink"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/auth/logout": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["logout"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/auth/login": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["login"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/account-companies": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get all account companies
         * @description Retrieves all active account companies for the current account
         */
        get: operations["getAllAccountCompanies"];
        put?: never;
        /**
         * Create account company
         * @description Creates a new account company
         */
        post: operations["createAccountCompany"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/invoices/{id}/status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update invoice status
         * @description Updates the status of an invoice
         */
        patch: operations["updateInvoiceStatus"];
        trace?: never;
    };
    "/api/invoices/{id}/pdf": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Download invoice PDF
         * @description Generates and downloads PDF for an invoice
         */
        get: operations["downloadInvoicePdf"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/invoices/{id}/delivery-logs": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get invoice delivery logs
         * @description Retrieves delivery logs for a specific invoice
         */
        get: operations["getInvoiceDeliveryLogs"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/invoices/next-number": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Generate next invoice number
         * @description Generates the next invoice number based on existing invoices
         */
        get: operations["getNextInvoiceNumber"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/participants/{participantId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get participant details
         * @description Gets detailed information about a specific hub participant
         */
        get: operations["getParticipantDetails"];
        put?: never;
        post?: never;
        /**
         * Remove participant
         * @description Removes a participant from the hub (admin permission required)
         */
        delete: operations["removeParticipant"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/hubs/{hubId}/chats/{channelId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get chat channel details
         * @description Gets details of a specific chat channel if the user has access
         */
        get: operations["getChannel"];
        put?: never;
        post?: never;
        /**
         * Delete custom chat channel
         * @description Deletes a custom chat channel (only by creator or hub admin)
         */
        delete: operations["deleteCustomChannel"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/brands/{id}/contacts": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get brand contacts
         * @description Retrieves all contacts for a specific brand
         */
        get: operations["getBrandContacts"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/auth/health": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["health"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        ErrorDetail: {
            field: string;
            code: string;
            message: string;
            params?: {
                [key: string]: unknown;
            };
        };
        ErrorInfo: {
            code: string;
            message: string;
            details?: components["schemas"]["ErrorDetail"][];
        };
        ErrorResponse: {
            error: components["schemas"]["ErrorInfo"];
        };
        PostUpdateRequest: {
            caption?: string;
            validPostContent?: boolean;
            media_uris?: string[];
            reviewer_notes?: string;
            reviewer_ids?: number[];
        };
        MediaItem: {
            url?: string;
            type?: string;
            /** Format: int64 */
            size?: number;
            /** Format: int32 */
            duration?: number;
            /** Format: int32 */
            width?: number;
            /** Format: int32 */
            height?: number;
            mime_type?: string;
        };
        PostCreator: {
            /** Format: int64 */
            id?: number;
            name?: string;
            email?: string;
        };
        PostPermissions: {
            can_edit?: boolean;
            can_review?: boolean;
            can_comment?: boolean;
        };
        PostResponse: {
            /** Format: int64 */
            id?: number;
            caption?: string;
            creator?: components["schemas"]["PostCreator"];
            permissions: components["schemas"]["PostPermissions"];
            media_uris?: components["schemas"]["MediaItem"][];
            /** @enum {string} */
            review_status?: PostResponseReview_status;
            reviewer_notes?: string;
            assigned_reviewers?: components["schemas"]["PostReviewer"][];
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        PostReviewer: {
            /** Format: int64 */
            id?: number;
            name?: string;
            email?: string;
            /** @enum {string} */
            status?: PostReviewerStatus;
        };
        InvoiceItemRequest: {
            description: string;
            quantity: number;
            unit_price: number;
            vat_rate?: number;
            /** Format: int32 */
            sort_order?: number;
        };
        InvoiceRecipientRequest: {
            email: string;
            /** @enum {string} */
            type: InvoiceRecipientRequestType;
            /** @enum {string} */
            source: InvoiceRecipientRequestSource;
            /** Format: int64 */
            brand_contact_id?: number;
        };
        InvoiceUpdateRequest: {
            currency?: string;
            notes?: string;
            template?: string;
            items?: components["schemas"]["InvoiceItemRequest"][];
            recipients?: components["schemas"]["InvoiceRecipientRequest"][];
            /** Format: int64 */
            issuer_id?: number;
            /** Format: int64 */
            recipient_id?: number;
            /** Format: int64 */
            bank_details_id?: number;
            invoice_number?: string;
            /** Format: date */
            issue_date?: string;
            /** Format: date */
            due_date?: string;
        };
        InvoiceItemResponse: {
            /** Format: int64 */
            id: number;
            description: string;
            quantity: number;
            unit_price: number;
            vat_rate: number;
            line_total: number;
            vat_amount: number;
            /** Format: int32 */
            sort_order: number;
        };
        InvoiceRecipientResponse: {
            /** Format: int64 */
            id: number;
            email: string;
            /** @enum {string} */
            type: InvoiceRecipientResponseType;
            /** @enum {string} */
            source: InvoiceRecipientResponseSource;
            /** Format: int64 */
            brand_contact_id?: number;
            /** Format: date-time */
            first_sent_at?: string;
            /** Format: date-time */
            last_sent_at?: string;
            /** Format: int32 */
            send_count: number;
        };
        InvoiceResponse: {
            /** Format: int64 */
            id: number;
            /** @enum {string} */
            status: InvoiceResponseStatus;
            template: string;
            currency: string;
            notes?: string;
            subtotal: number;
            items: components["schemas"]["InvoiceItemResponse"][];
            recipients: components["schemas"]["InvoiceRecipientResponse"][];
            /** Format: int64 */
            account_id: number;
            invoice_number: string;
            /** Format: date */
            issue_date: string;
            /** Format: date */
            due_date: string;
            total_vat: number;
            total_amount: number;
            /** Format: date-time */
            created_at: string;
            /** Format: date-time */
            updated_at: string;
            /** Format: int64 */
            issuer_id?: number;
            /** Format: int64 */
            recipient_id?: number;
            /** Format: int64 */
            bank_details_id?: number;
            issuer_snapshot_hash?: string;
            recipient_snapshot_hash?: string;
            bank_details_snapshot_hash?: string;
            /** Format: int32 */
            days_until_due?: number;
            is_overdue?: boolean;
            next_invoice_number?: string;
        };
        CollaborationHubUpdateRequest: {
            name: string;
            description?: string;
        };
        CollaborationHubResponse: {
            /** Format: int64 */
            id: number;
            name: string;
            /** Format: int64 */
            brandId: number;
            brandName: string;
            description?: string;
            /** @enum {string} */
            myRole: CollaborationHubResponseMyRole;
            /** Format: date-time */
            createdAt: string;
        };
        HubParticipantUpdateRoleRequest: {
            /** @enum {string} */
            role: HubParticipantUpdateRoleRequestRole;
        };
        HubParticipantResponse: {
            /** Format: int64 */
            id: number;
            /** Format: int64 */
            userId?: number;
            email?: string;
            name?: string;
            /** @enum {string} */
            role: HubParticipantResponseRole;
            isExternal: boolean;
            /** Format: date-time */
            invitedAt: string;
            /** Format: date-time */
            joinedAt?: string;
            status: string;
        };
        ChatChannelParticipantRequest: {
            participant_ids: number[];
        };
        CollaborationBriefUpdateRequest: {
            title: string;
            body?: string;
        };
        CollaborationBriefResponse: {
            /** Format: int64 */
            id: number;
            /** Format: int64 */
            hubId: number;
            title: string;
            body?: string;
            /** Format: int64 */
            createdByParticipantId: number;
            createdByParticipantName?: string;
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
        };
        PostCommentUpdateRequest: {
            content: string;
        };
        CommentAuthor: {
            /** Format: int64 */
            id?: number;
            name?: string;
            email?: string;
        };
        CommentPermissions: {
            can_edit?: boolean;
            can_delete?: boolean;
        };
        MentionDto: {
            name: string;
            email?: string;
            /** Format: int64 */
            participant_id: number;
            is_external?: boolean;
        };
        PostCommentResponse: {
            /** Format: int64 */
            id?: number;
            content?: string;
            author?: components["schemas"]["CommentAuthor"];
            permissions?: components["schemas"]["CommentPermissions"];
            mentions?: components["schemas"]["MentionDto"][];
            /** Format: int64 */
            post_id?: number;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
        };
        AttachmentRequestDto: {
            url: string;
            filename: string;
            /** Format: int64 */
            size: number;
            type?: string;
            content_type?: string;
        };
        ChatMessageUpdateRequest: {
            content?: string;
            attachment_uris?: string[];
            attachments?: components["schemas"]["AttachmentRequestDto"][];
        };
        AttachmentDto: {
            url: string;
            filename: string;
            /** Format: int64 */
            size: number;
            type?: string;
            content_type?: string;
        };
        ChatMessageResponse: {
            /** Format: int64 */
            id?: number;
            content?: string;
            sender?: components["schemas"]["ChatParticipantDto"];
            mentions?: components["schemas"]["MentionDto"][];
            attachments?: components["schemas"]["AttachmentDto"][];
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
            /** Format: date-time */
            edited_at?: string;
            is_edited?: boolean;
        };
        ChatParticipantDto: {
            /** Format: int64 */
            id?: number;
            name?: string;
            email?: string;
            /** @enum {string} */
            role?: ChatParticipantDtoRole;
            is_external?: boolean;
        };
        BrandContactRequest: {
            name: string;
            email: string;
            notes?: string;
            /** Format: int64 */
            id?: number;
        };
        BrandUpdateRequest: {
            name: string;
            phone?: string;
            email?: string;
            website?: string;
            contacts?: components["schemas"]["BrandContactRequest"][];
            company_name: string;
            address_street?: string;
            address_city?: string;
            address_postal_code?: string;
            address_country?: string;
            vat_number?: string;
            registration_number?: string;
        };
        BrandContactResponse: {
            /** Format: int64 */
            id: number;
            name: string;
            email: string;
            notes?: string;
            /** Format: int64 */
            brand_id: number;
            /** Format: int64 */
            account_id: number;
            /** Format: date-time */
            created_at: string;
            /** Format: date-time */
            updated_at: string;
        };
        BrandResponse: {
            /** Format: int64 */
            id: number;
            name: string;
            phone?: string;
            email?: string;
            website?: string;
            contacts: components["schemas"]["BrandContactResponse"][];
            /** Format: int64 */
            account_id: number;
            company_name: string;
            address_street?: string;
            address_city?: string;
            address_postal_code?: string;
            address_country?: string;
            vat_number?: string;
            registration_number?: string;
            /** Format: date-time */
            created_at: string;
            /** Format: date-time */
            updated_at: string;
        };
        BankDetailsUpdateRequest: {
            name?: string;
            iban?: string;
            bank_name?: string;
            bic_swift?: string;
        };
        BankDetailsResponse: {
            /** Format: int64 */
            id: number;
            name: string;
            iban?: string;
            /** Format: int64 */
            account_id: number;
            bank_name?: string;
            bic_swift?: string;
            /** Format: date-time */
            created_at: string;
            /** Format: date-time */
            updated_at: string;
        };
        AccountCompanyUpdateRequest: {
            phone?: string;
            email?: string;
            website?: string;
            company_name?: string;
            address_street?: string;
            address_city?: string;
            address_postal_code?: string;
            address_country?: string;
            vat_number?: string;
            registration_number?: string;
        };
        AccountCompanyResponse: {
            /** Format: int64 */
            id: number;
            phone?: string;
            email?: string;
            website?: string;
            /** Format: int64 */
            account_id: number;
            company_name: string;
            address_street?: string;
            address_city?: string;
            address_postal_code?: string;
            address_country?: string;
            vat_number?: string;
            registration_number?: string;
            /** Format: date-time */
            created_at: string;
            /** Format: date-time */
            updated_at: string;
        };
        PostReviewRequest: {
            /** @enum {string} */
            status: PostReviewRequestStatus;
            review_notes?: string;
        };
        PostReviewResponse: {
            /** @enum {string} */
            status?: PostReviewResponseStatus;
            /** Format: int64 */
            post_id?: number;
            /** Format: int64 */
            reviewer_id?: number;
            reviewer_name?: string;
            reviewer_email?: string;
            review_notes?: string;
            /** Format: date-time */
            assigned_at?: string;
            /** Format: date-time */
            reviewed_at?: string;
        };
        PostCommentCreateRequest: {
            content: string;
        };
        FileUploadResponse: {
            url: string;
            filename: string;
            /** Format: int64 */
            size: number;
            /** @enum {string} */
            type: FileUploadResponseType;
            mime_type: string;
        };
        InvoiceCreateRequest: {
            template?: string;
            currency?: string;
            notes?: string;
            items: components["schemas"]["InvoiceItemRequest"][];
            recipients: components["schemas"]["InvoiceRecipientRequest"][];
            /** Format: int64 */
            issuer_id: number;
            /** Format: int64 */
            recipient_id: number;
            /** Format: int64 */
            bank_details_id?: number;
            invoice_number: string;
            /** Format: date */
            issue_date: string;
            /** Format: date */
            due_date: string;
        };
        InvoiceEmailResponse: {
            success: boolean;
            message: string;
            /** Format: int32 */
            recipients_count?: number;
            /** Format: date-time */
            sent_at?: string;
        };
        CollaborationHubCreateRequest: {
            name: string;
            /** Format: int64 */
            brandId: number;
            description?: string;
        };
        PostCreateRequest: {
            caption?: string;
            validPostContent?: boolean;
            media_uris?: string[];
            reviewer_notes?: string;
            reviewer_ids?: number[];
        };
        HubParticipantInviteRequest: {
            participants: components["schemas"]["ParticipantInviteItem"][];
        };
        ParticipantInviteItem: {
            type: string;
            email?: string;
            name?: string;
            /** @enum {string} */
            role: ParticipantInviteItemRole;
            validExternalParticipant?: boolean;
            validInternalParticipant?: boolean;
            validParticipantType?: boolean;
            validExternalParticipantName?: boolean;
            validBrandContactParticipant?: boolean;
            /** Format: int64 */
            user_id?: number;
            /** Format: int64 */
            brand_contact_id?: number;
        };
        HubParticipantInviteResponse: {
            invited: components["schemas"]["InvitedParticipant"][];
        };
        InvitedParticipant: {
            /** Format: int64 */
            id: number;
            email?: string;
            name?: string;
            /** @enum {string} */
            role: InvitedParticipantRole;
            status: string;
            /** Format: int64 */
            user_id?: number;
            is_external: boolean;
            /** Format: date-time */
            invited_at: string;
            /** Format: date-time */
            joined_at?: string;
        };
        FileValidationResponse: {
            valid: boolean;
            message: string;
            file_url: string;
            /** Format: int64 */
            file_size?: number;
            content_type?: string;
            validation_details?: components["schemas"]["ValidationDetails"];
        };
        ValidationDetails: {
            size_check_passed?: boolean;
            content_type_check_passed?: boolean;
            file_exists?: boolean;
            /** Format: int64 */
            max_size_limit?: number;
            expected_content_type?: string;
        };
        PresignedUploadResponse: {
            key?: string;
            presigned_url?: string;
            final_url?: string;
            content_type?: string;
            /** Format: int64 */
            max_file_size?: number;
            /** Format: int32 */
            expires_in_minutes?: number;
        };
        /** @description Media creation request */
        MediaRecordCreateRequest: {
            filename: string;
            file_url: string;
            /** Format: int64 */
            file_size: number;
            mime_type: string;
        };
        /** @description Batch upload request */
        BatchPresignedUploadRequest: {
            files: components["schemas"]["FileUploadRequest"][];
        };
        FileUploadRequest: {
            file_name: string;
            content_type: string;
            /** Format: int64 */
            max_file_size?: number;
        };
        BatchPresignedUploadResponse: {
            upload_urls?: components["schemas"]["PresignedUploadResponse"][];
            /** Format: int32 */
            expires_in_minutes?: number;
        };
        ChatChannelCreateRequest: {
            name: string;
            description?: string;
            participant_ids: number[];
        };
        ChatChannelResponse: {
            /** Format: int64 */
            id?: number;
            name?: string;
            description?: string;
            /** @enum {string} */
            scope?: ChatChannelResponseScope;
            /** Format: int64 */
            created_by_participant_id?: number;
            /** Format: int64 */
            participant_count?: number;
            participants?: components["schemas"]["ChatParticipantDto"][];
            /** Format: int64 */
            unread_count?: number;
            last_message?: components["schemas"]["ChatMessageResponse"];
            can_access?: boolean;
            can_write?: boolean;
            can_manage?: boolean;
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            last_activity_at?: string;
        };
        CollaborationBriefCreateRequest: {
            title: string;
            body?: string;
        };
        ChatMessageRequest: {
            content?: string;
            attachment_uris?: string[];
            attachments?: components["schemas"]["AttachmentRequestDto"][];
        };
        BrandCreateRequest: {
            name: string;
            phone?: string;
            email?: string;
            website?: string;
            contacts?: components["schemas"]["BrandContactRequest"][];
            company_name: string;
            address_street?: string;
            address_city?: string;
            address_postal_code?: string;
            address_country?: string;
            vat_number?: string;
            registration_number?: string;
        };
        BankDetailsCreateRequest: {
            name: string;
            iban?: string;
            bank_name?: string;
            bic_swift?: string;
        };
        EmailVerificationRequest: {
            token: string;
        };
        EmailVerificationResponse: {
            message?: string;
            email?: string;
            account_enabled?: boolean;
        };
        RegistrationRequest: {
            email: string;
            password: string;
            account_name: string;
            display_name: string;
        };
        RegistrationResponse: {
            message?: string;
            email?: string;
            verification_required?: boolean;
        };
        AuthenticationResponse: {
            user: components["schemas"]["UserInfo"];
            access_token: string;
            token_type: string;
            /** Format: int64 */
            expires_in: number;
            redirect_context?: components["schemas"]["GeneralAccessRedirectContext"] | components["schemas"]["HubAccessRedirectContext"] | components["schemas"]["InvoiceAccessRedirectContext"];
        };
        GeneralAccessRedirectContext: {
            type: "GeneralAccessRedirectContext";
        } & (Omit<components["schemas"]["RedirectContext"], "type"> & {
            defaultPage?: string;
        });
        HubAccessRedirectContext: {
            type: "HubAccessRedirectContext";
        } & (Omit<components["schemas"]["RedirectContext"], "type"> & {
            /** Format: int64 */
            hubId: number;
            role?: string;
        });
        InvoiceAccessRedirectContext: {
            type: "InvoiceAccessRedirectContext";
        } & (Omit<components["schemas"]["RedirectContext"], "type"> & {
            /** Format: int64 */
            invoiceId: number;
            accessType?: string;
        });
        /** @enum {string} */
        Permission: Permission;
        RedirectContext: {
            /** @enum {string} */
            type?: RedirectContextType;
            valid?: boolean;
            targetUrl?: string;
        };
        UserInfo: {
            /** Format: int64 */
            id: number;
            email: string;
            /** @enum {string} */
            role: UserInfoRole;
            permissions: components["schemas"]["Permission"][];
            internal: boolean;
            display_name: string;
            /** Format: int64 */
            account_id: number;
        };
        LoginRequest: {
            email: string;
            password: string;
        };
        AccountCompanyCreateRequest: {
            phone?: string;
            email?: string;
            website?: string;
            company_name: string;
            address_street?: string;
            address_city?: string;
            address_postal_code?: string;
            address_country?: string;
            vat_number?: string;
            registration_number?: string;
        };
        InvoiceStatusUpdateRequest: {
            /** @enum {string} */
            status: InvoiceStatusUpdateRequestStatus;
            note?: string;
        };
        PostCommentItem: {
            /** Format: int64 */
            id?: number;
            content?: string;
            author?: components["schemas"]["CommentAuthor"];
            mentions?: components["schemas"]["MentionDto"][];
            /** Format: date-time */
            created_at?: string;
            /** Format: date-time */
            updated_at?: string;
            can_edit?: boolean;
            can_delete?: boolean;
        };
        PostCommentListResponse: {
            comments: components["schemas"]["PostCommentItem"][];
            /** Format: int64 */
            total_count?: number;
            /** Format: int32 */
            page_size?: number;
            /** Format: int32 */
            current_page?: number;
            /** Format: int32 */
            total_pages?: number;
            has_next?: boolean;
            has_previous?: boolean;
        };
        InvoiceListItemDto: {
            /** Format: int64 */
            id: number;
            /** @enum {string} */
            status: InvoiceListItemDtoStatus;
            subtotal: number;
            currency: string;
            invoice_number: string;
            /** Format: date */
            issue_date: string;
            /** Format: date */
            due_date: string;
            total_vat: number;
            total_amount: number;
            /** Format: int64 */
            recipient_id?: number;
            recipient_name?: string;
            /** Format: int32 */
            days_until_due?: number;
            is_overdue?: boolean;
        };
        PageResponseInvoiceListItemDto: {
            content?: components["schemas"]["InvoiceListItemDto"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
        InvoiceDeliveryLogResponse: {
            /** Format: int64 */
            id: number;
            /** Format: int64 */
            invoice_id: number;
            recipient_email?: string;
            delivery_status: string;
            error_message?: string;
            /** Format: date-time */
            created_at: string;
        };
        NextInvoiceNumberResponse: {
            next_invoice_number: string;
            is_generated: boolean;
            pattern_detected?: string;
        };
        CollaborationHubListItemDto: {
            /** Format: int64 */
            id: number;
            name: string;
            brandName: string;
            /** Format: int64 */
            brandId: number;
            /** @enum {string} */
            myRole: CollaborationHubListItemDtoMyRole;
            /** Format: date-time */
            createdAt: string;
        };
        PageResponseCollaborationHubListItemDto: {
            content?: components["schemas"]["CollaborationHubListItemDto"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
        PostListItemResponse: {
            /** Format: int64 */
            id?: number;
            caption?: string;
            creator?: components["schemas"]["PostCreator"];
            /** Format: int32 */
            media_count?: number;
            media_uris?: components["schemas"]["MediaItem"][];
            /** @enum {string} */
            review_status?: PostListItemResponseReview_status;
            reviewer_notes?: string;
            /** @enum {string} */
            my_review_status?: PostListItemResponseMy_review_status;
            /** Format: int32 */
            comment_count?: number;
            assigned_reviewers?: components["schemas"]["PostReviewer"][];
            /** Format: date-time */
            created_at?: string;
            can_edit?: boolean;
            can_review?: boolean;
            can_comment?: boolean;
        };
        PostListResponse: {
            content?: components["schemas"]["PostListItemResponse"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
        HubParticipantListResponse: {
            filters?: components["schemas"]["ParticipantFilters"];
            content?: components["schemas"]["HubParticipantResponse"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
        ParticipantFilters: {
            availableStatuses?: string[];
            availableRoles?: ParticipantFiltersAvailableRoles[];
            currentStatus?: string;
            /** @enum {string} */
            currentRole?: ParticipantFiltersCurrentRole;
        };
        PageResponseChatChannelResponse: {
            content?: components["schemas"]["ChatChannelResponse"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
        CollaborationBriefListItemDto: {
            /** Format: int64 */
            id: number;
            title: string;
            bodyPreview?: string;
            /** Format: int64 */
            createdByParticipantId: number;
            createdByParticipantName?: string;
            /** Format: date-time */
            createdAt: string;
            /** Format: date-time */
            updatedAt: string;
        };
        PageResponseCollaborationBriefListItemDto: {
            content?: components["schemas"]["CollaborationBriefListItemDto"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
        ChatChannelInfo: {
            /** Format: int64 */
            id?: number;
            name?: string;
            /** @enum {string} */
            scope?: ChatChannelInfoScope;
        };
        ChatMessageListResponse: {
            messages?: components["schemas"]["ChatMessageResponse"][];
            pagination?: components["schemas"]["PageResponseChatMessageResponse"];
            channel_info?: components["schemas"]["ChatChannelInfo"];
            has_more?: boolean;
        };
        PageResponseChatMessageResponse: {
            content?: components["schemas"]["ChatMessageResponse"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
        BrandListItemDto: {
            /** Format: int64 */
            id: number;
            name: string;
            email?: string;
            phone?: string;
            website?: string;
            company_name: string;
        };
        PageResponseBrandListItemDto: {
            content?: components["schemas"]["BrandListItemDto"][];
            /** Format: int32 */
            page?: number;
            /** Format: int32 */
            size?: number;
            /** Format: int64 */
            total_elements?: number;
            /** Format: int32 */
            total_pages?: number;
            first?: boolean;
            last?: boolean;
            has_next?: boolean;
            has_previous?: boolean;
            /** Format: int32 */
            number_of_elements?: number;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    getPost: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Post ID */
                postId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updatePost: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Post ID */
                postId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PostUpdateRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deletePost: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Post ID */
                postId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Post deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description User lacks permission to delete this post */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Post not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getInvoiceById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Invoice ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Invoice retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["InvoiceResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateInvoice: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Invoice ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["InvoiceUpdateRequest"];
            };
        };
        responses: {
            /** @description Invoice updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["InvoiceResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteInvoice: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Invoice ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Invoice deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getHubDetails: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Hub details retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["CollaborationHubResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateHub: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CollaborationHubUpdateRequest"];
            };
        };
        responses: {
            /** @description Hub updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["CollaborationHubResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Hub name already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteHub: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Hub deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateParticipantRole: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Participant ID */
                participantId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["HubParticipantUpdateRoleRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["HubParticipantResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    addParticipantsToChannel: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Channel ID */
                channelId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ChatChannelParticipantRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    removeParticipantsFromChannel: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Channel ID */
                channelId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ChatChannelParticipantRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getBriefById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Brief ID */
                briefId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Brief retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["CollaborationBriefResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brief or collaboration hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateBrief: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Brief ID */
                briefId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CollaborationBriefUpdateRequest"];
            };
        };
        responses: {
            /** @description Brief updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["CollaborationBriefResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brief or collaboration hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brief title already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteBrief: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Brief ID */
                briefId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Brief deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brief or collaboration hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getComment: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Comment ID */
                commentId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostCommentResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateComment: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Comment ID */
                commentId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PostCommentUpdateRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostCommentResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteComment: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Comment ID */
                commentId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateMessage: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Channel ID */
                channelId: number;
                /** @description Message ID */
                messageId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ChatMessageUpdateRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ChatMessageResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteMessage: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Channel ID */
                channelId: number;
                /** @description Message ID */
                messageId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getBrandById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Brand ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Brand retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BrandResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brand not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateBrand: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Brand ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BrandUpdateRequest"];
            };
        };
        responses: {
            /** @description Brand updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BrandResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brand not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brand name already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteBrand: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Brand ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Brand deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brand not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getBankDetailsById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Bank detail ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully retrieved bank detail */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BankDetailsResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Bank detail not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateBankDetails: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Bank detail ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BankDetailsUpdateRequest"];
            };
        };
        responses: {
            /** @description Bank detail updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BankDetailsResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Bank detail not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteBankDetails: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Bank detail ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Bank detail deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Bank detail not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getAccountCompanyById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Account company ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully retrieved account company */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["AccountCompanyResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Account company not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateAccountCompany: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Account company ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AccountCompanyUpdateRequest"];
            };
        };
        responses: {
            /** @description Account company updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["AccountCompanyResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Account company not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteAccountCompany: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Account company ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Account company deleted successfully */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Account company not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    submitReview: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Post ID */
                postId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PostReviewRequest"];
            };
        };
        responses: {
            /** @description Review submitted successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostReviewResponse"];
                };
            };
            /** @description Invalid request data or validation errors */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description User lacks permission to review this post */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Post not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getCommentsForPost: {
        parameters: {
            query?: {
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size */
                size?: number;
            };
            header?: never;
            path: {
                /** @description Post ID */
                postId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostCommentListResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createComment: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Post ID */
                postId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PostCommentCreateRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostCommentResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    uploadMedia: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "multipart/form-data": {
                    /**
                     * Format: binary
                     * @description Media file to upload
                     */
                    file: string;
                };
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["FileUploadResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getAllInvoices: {
        parameters: {
            query?: {
                /** @description Filter by invoice status */
                status?: PathsApiInvoicesGetParametersQueryStatus;
                /** @description Filter from date (inclusive) */
                fromDate?: string;
                /** @description Filter to date (inclusive) */
                toDate?: string;
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size (max 100) */
                size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Invoices retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PageResponseInvoiceListItemDto"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createInvoice: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["InvoiceCreateRequest"];
            };
        };
        responses: {
            /** @description Invoice created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["InvoiceResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    sendInvoiceEmail: {
        parameters: {
            query?: {
                /** @description Force send even if already sent */
                force?: boolean;
            };
            header?: never;
            path: {
                /** @description Invoice ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Invoice sent successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["InvoiceEmailResponse"];
                };
            };
            /** @description Invalid request, send failed, or duplicate send attempt */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getHubs: {
        parameters: {
            query?: {
                /** @description Filter hubs by name (case-insensitive partial match) */
                name?: string;
                /** @description Filter hubs by brand ID */
                brandId?: number;
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size (max 100) */
                size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Hubs retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PageResponseCollaborationHubListItemDto"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createHub: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CollaborationHubCreateRequest"];
            };
        };
        responses: {
            /** @description Hub created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["CollaborationHubResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Hub name already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getPosts: {
        parameters: {
            query?: {
                /** @description Filter type */
                filter?: string;
                /** @description Review status filter */
                status?: PathsApiHubsHubIdPostsGetParametersQueryStatus;
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size (max 100) */
                size?: number;
            };
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostListResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createPost: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["PostCreateRequest"];
            };
        };
        responses: {
            /** @description Post created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PostResponse"];
                };
            };
            /** @description Invalid request data or validation errors */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description User lacks permission to create posts in this hub */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getHubParticipants: {
        parameters: {
            query?: {
                /** @description Filter by participant role */
                role?: PathsApiHubsHubIdParticipantsGetParametersQueryRole;
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size (max 100) */
                size?: number;
            };
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["HubParticipantListResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    inviteParticipants: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["HubParticipantInviteRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["HubParticipantInviteResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    resendInvitation: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Participant ID */
                participantId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    validateUploadedFile: {
        parameters: {
            query: {
                /** @description File URL to validate */
                fileUrl: string;
                /** @description Expected content type */
                contentType?: string;
                /** @description Max file size */
                maxFileSize?: number;
            };
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["FileValidationResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    generatePresignedUploadUrl: {
        parameters: {
            query: {
                /** @description File name */
                fileName: string;
                /** @description Content type */
                contentType: string;
                /** @description Max file size in bytes (optional) */
                maxFileSize?: number;
            };
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PresignedUploadResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    generatePresignedDownloadUrl: {
        parameters: {
            query: {
                /** @description File URL to download */
                fileUrl: string;
            };
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": {
                        [key: string]: string;
                    };
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createMediaRecord: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["MediaRecordCreateRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["FileUploadResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    generateBatchPresignedUploadUrls: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BatchPresignedUploadRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BatchPresignedUploadResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getChannels: {
        parameters: {
            query?: {
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size (max 100) */
                size?: number;
            };
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PageResponseChatChannelResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createCustomChannel: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ChatChannelCreateRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ChatChannelResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getBriefs: {
        parameters: {
            query?: {
                /** @description Filter briefs by title (case-insensitive partial match) */
                title?: string;
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size (max 100) */
                size?: number;
            };
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Briefs retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PageResponseCollaborationBriefListItemDto"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Collaboration hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createBrief: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CollaborationBriefCreateRequest"];
            };
        };
        responses: {
            /** @description Brief created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["CollaborationBriefResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Collaboration hub not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brief title already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getMessages: {
        parameters: {
            query?: {
                /** @description Maximum number of messages to return (default: 50, max: 100) */
                limit?: number;
                /** @description Get messages before this message ID (for pagination) */
                before?: number;
            };
            header?: never;
            path: {
                /** @description Channel ID */
                channelId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ChatMessageListResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    sendMessage: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Channel ID */
                channelId: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["ChatMessageRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ChatMessageResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getBrands: {
        parameters: {
            query?: {
                /** @description Filter brands by name (case-insensitive partial match) */
                name?: string;
                /** @description Page number (0-based) */
                page?: number;
                /** @description Page size (max 100) */
                size?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Brands retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["PageResponseBrandListItemDto"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createBrand: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BrandCreateRequest"];
            };
        };
        responses: {
            /** @description Brand created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BrandResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brand name already exists */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getAllBankDetails: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully retrieved bank details */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BankDetailsResponse"][];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createBankDetails: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["BankDetailsCreateRequest"];
            };
        };
        responses: {
            /** @description Bank detail created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BankDetailsResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    verifyEmail: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["EmailVerificationRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["EmailVerificationResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    register: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RegistrationRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["RegistrationResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    refresh: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["AuthenticationResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    authenticateWithMagicLink: {
        parameters: {
            query: {
                /** @description Magic link token */
                token: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["AuthenticationResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    logout: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    login: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["LoginRequest"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["AuthenticationResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getAllAccountCompanies: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Successfully retrieved account companies */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["AccountCompanyResponse"][];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    createAccountCompany: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["AccountCompanyCreateRequest"];
            };
        };
        responses: {
            /** @description Account company created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["AccountCompanyResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    updateInvoiceStatus: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Invoice ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["InvoiceStatusUpdateRequest"];
            };
        };
        responses: {
            /** @description Invoice status updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["InvoiceResponse"];
                };
            };
            /** @description Invalid request data */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    downloadInvoicePdf: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Invoice ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description PDF generated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getInvoiceDeliveryLogs: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Invoice ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Delivery logs retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["InvoiceDeliveryLogResponse"][];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getNextInvoiceNumber: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Next invoice number generated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["NextInvoiceNumberResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getParticipantDetails: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Participant ID */
                participantId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["HubParticipantResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    removeParticipant: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Participant ID */
                participantId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getChannel: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Channel ID */
                channelId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ChatChannelResponse"];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    deleteCustomChannel: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Hub ID */
                hubId: number;
                /** @description Channel ID */
                channelId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    getBrandContacts: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Brand ID */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Brand contacts retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["BrandContactResponse"][];
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Authentication required */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Insufficient permissions */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Brand not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
    health: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Bad Request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Conflict */
            409: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
            /** @description Internal Server Error */
            500: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ErrorResponse"];
                };
            };
        };
    };
}
export enum PathsApiInvoicesGetParametersQueryStatus {
    draft = "draft",
    sent = "sent",
    overdue = "overdue",
    paid = "paid"
}
export enum PathsApiHubsHubIdPostsGetParametersQueryStatus {
    pending = "pending",
    approved = "approved",
    rework = "rework"
}
export enum PathsApiHubsHubIdParticipantsGetParametersQueryRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum PostResponseReview_status {
    pending = "pending",
    approved = "approved",
    rework = "rework"
}
export enum PostReviewerStatus {
    pending = "pending",
    approved = "approved",
    rework = "rework"
}
export enum InvoiceRecipientRequestType {
    original = "original",
    copy = "copy"
}
export enum InvoiceRecipientRequestSource {
    brand_contact = "brand_contact",
    manual = "manual"
}
export enum InvoiceRecipientResponseType {
    original = "original",
    copy = "copy"
}
export enum InvoiceRecipientResponseSource {
    brand_contact = "brand_contact",
    manual = "manual"
}
export enum InvoiceResponseStatus {
    draft = "draft",
    sent = "sent",
    overdue = "overdue",
    paid = "paid"
}
export enum CollaborationHubResponseMyRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum HubParticipantUpdateRoleRequestRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum HubParticipantResponseRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum ChatParticipantDtoRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum PostReviewRequestStatus {
    pending = "pending",
    approved = "approved",
    rework = "rework"
}
export enum PostReviewResponseStatus {
    pending = "pending",
    approved = "approved",
    rework = "rework"
}
export enum FileUploadResponseType {
    image = "image",
    video = "video"
}
export enum ParticipantInviteItemRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum InvitedParticipantRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum ChatChannelResponseScope {
    general = "general",
    custom = "custom"
}
export enum Permission {
    ACCOUNT_READ = "ACCOUNT_READ",
    ACCOUNT_WRITE = "ACCOUNT_WRITE",
    ACCOUNT_DELETE = "ACCOUNT_DELETE",
    USER_READ = "USER_READ",
    USER_WRITE = "USER_WRITE",
    USER_DELETE = "USER_DELETE",
    USER_INVITE = "USER_INVITE",
    HUB_READ = "HUB_READ",
    HUB_WRITE = "HUB_WRITE",
    HUB_DELETE = "HUB_DELETE",
    HUB_INVITE = "HUB_INVITE",
    HUB_PARTICIPANT_READ = "HUB_PARTICIPANT_READ",
    HUB_PARTICIPANT_WRITE = "HUB_PARTICIPANT_WRITE",
    HUB_PARTICIPANT_INVITE = "HUB_PARTICIPANT_INVITE",
    HUB_PARTICIPANT_MANAGE = "HUB_PARTICIPANT_MANAGE",
    CONTENT_READ = "CONTENT_READ",
    CONTENT_WRITE = "CONTENT_WRITE",
    CONTENT_DELETE = "CONTENT_DELETE",
    CONTENT_REVIEW = "CONTENT_REVIEW",
    POST_READ = "POST_READ",
    POST_WRITE = "POST_WRITE",
    POST_DELETE = "POST_DELETE",
    POST_UPDATE = "POST_UPDATE",
    POST_COMMENT = "POST_COMMENT",
    INVOICE_READ = "INVOICE_READ",
    INVOICE_WRITE = "INVOICE_WRITE",
    INVOICE_DELETE = "INVOICE_DELETE",
    INVOICE_SEND = "INVOICE_SEND",
    BRAND_READ = "BRAND_READ",
    BRAND_WRITE = "BRAND_WRITE",
    BRAND_DELETE = "BRAND_DELETE",
    COMPANY_READ = "COMPANY_READ",
    COMPANY_WRITE = "COMPANY_WRITE",
    COMPANY_DELETE = "COMPANY_DELETE",
    BANK_READ = "BANK_READ",
    BANK_WRITE = "BANK_WRITE",
    BANK_DELETE = "BANK_DELETE",
    CHAT_READ = "CHAT_READ",
    CHAT_WRITE = "CHAT_WRITE",
    CHAT_CHANNEL_READ = "CHAT_CHANNEL_READ",
    CHAT_CHANNEL_MANAGE = "CHAT_CHANNEL_MANAGE",
    BRIEF_READ = "BRIEF_READ",
    BRIEF_WRITE = "BRIEF_WRITE",
    BRIEF_DELETE = "BRIEF_DELETE",
    BRIEF_UPDATE = "BRIEF_UPDATE"
}
export enum RedirectContextType {
    HUB_ACCESS = "HUB_ACCESS",
    INVOICE_ACCESS = "INVOICE_ACCESS",
    GENERAL_ACCESS = "GENERAL_ACCESS"
}
export enum UserInfoRole {
    ADMIN = "ADMIN",
    EXTERNAL_PARTICIPANT = "EXTERNAL_PARTICIPANT"
}
export enum InvoiceStatusUpdateRequestStatus {
    draft = "draft",
    sent = "sent",
    overdue = "overdue",
    paid = "paid"
}
export enum InvoiceListItemDtoStatus {
    draft = "draft",
    sent = "sent",
    overdue = "overdue",
    paid = "paid"
}
export enum CollaborationHubListItemDtoMyRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum PostListItemResponseReview_status {
    pending = "pending",
    approved = "approved",
    rework = "rework"
}
export enum PostListItemResponseMy_review_status {
    pending = "pending",
    approved = "approved",
    rework = "rework"
}
export enum ParticipantFiltersAvailableRoles {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum ParticipantFiltersCurrentRole {
    admin = "admin",
    content_creator = "content_creator",
    reviewer = "reviewer",
    reviewer_creator = "reviewer_creator"
}
export enum ChatChannelInfoScope {
    general = "general",
    custom = "custom"
}
